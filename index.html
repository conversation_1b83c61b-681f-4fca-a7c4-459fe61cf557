<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Glassmorphism UI Showcase</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Theme Toggle -->
    <div class="theme-toggle">
        <button id="themeToggle" class="theme-btn" aria-label="Toggle between dark and light themes">
            <span class="theme-icon">🌙</span>
        </button>
    </div>

    <!-- Dark Theme - Mobile App Interface -->
    <main class="container dark-theme" id="darkTheme">
        <!-- Atmospheric Background Elements -->
        <div class="background-atmosphere">
            <div class="gradient-orb orb-1"></div>
            <div class="gradient-orb orb-2"></div>
            <div class="gradient-orb orb-3"></div>
        </div>

        <!-- Top Glowing Light Bar -->
        <div class="top-light-bar" aria-hidden="true"></div>

        <!-- Main Glass Card -->
        <article class="glass-card main-card">
            <!-- Card Header -->
            <header class="card-header">
                <p class="card-subtitle">This is amazing, let's make it fit and push it to production</p>
                
                <!-- Social Icons -->
                <nav class="social-icons" aria-label="Social media links">
                    <a href="#" class="social-icon" aria-label="Instagram">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                    </a>
                    <a href="#" class="social-icon" aria-label="Twitter">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                    </a>
                    <a href="#" class="social-icon" aria-label="LinkedIn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                    </a>
                </nav>
            </header>

            <!-- Geometric Pattern Section -->
            <section class="geometric-pattern" aria-label="Design visualization">
                <div class="pattern-container">
                    <!-- Main circles with connecting lines -->
                    <div class="circle-group">
                        <div class="circle circle-1"></div>
                        <div class="circle circle-2"></div>
                        <div class="circle circle-3"></div>
                        <div class="circle circle-4"></div>
                        <div class="connecting-lines">
                            <div class="line line-1"></div>
                            <div class="line line-2"></div>
                            <div class="line line-3"></div>
                        </div>
                    </div>
                    
                    <!-- Blue accent dots -->
                    <div class="accent-dots">
                        <div class="dot dot-1"></div>
                        <div class="dot dot-2"></div>
                    </div>
                    
                    <!-- Yellow accent element -->
                    <div class="yellow-accent"></div>
                </div>
            </section>

            <!-- Main Content -->
            <section class="card-content">
                <h1 class="main-heading">Design Smarter, Not Harder</h1>
                <p class="main-description">
                    Unlock powerful tools to create pixel-perfect designs in record time.
                </p>
                
                <button class="cta-button" type="button">
                    Get started
                </button>
            </section>
        </article>

        <!-- Side Glass Elements -->
        <div class="side-glass-elements" aria-hidden="true">
            <div class="side-glass left-glass"></div>
            <div class="side-glass right-glass"></div>
        </div>
    </main>

    <!-- Light Theme - Audio Interface -->
    <main class="container light-theme" id="lightTheme" style="display: none;">
        <!-- Audio Control Panels -->
        <div class="audio-interface">
            <!-- Left Panel - Level and Width Controls -->
            <section class="control-panel panel-left">
                <header class="panel-header">
                    <button class="power-btn" aria-label="Power toggle">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                        </svg>
                    </button>
                </header>
                
                <div class="control-group">
                    <div class="knob-control">
                        <div class="knob" data-value="75">
                            <div class="knob-indicator"></div>
                        </div>
                        <label class="control-label">LEVEL</label>
                    </div>
                </div>
                
                <div class="control-group">
                    <div class="knob-control">
                        <div class="knob" data-value="45">
                            <div class="knob-indicator"></div>
                        </div>
                        <label class="control-label">WIDTH</label>
                    </div>
                </div>
                
                <div class="toggle-group">
                    <label class="toggle-label">ADV</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="adv1" checked>
                        <label for="adv1" class="toggle-slider"></label>
                    </div>
                </div>
            </section>

            <!-- Center Panel - EQ and Sliders -->
            <section class="control-panel panel-center">
                <header class="panel-header">
                    <button class="power-btn" aria-label="Power toggle">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                        </svg>
                    </button>
                </header>
                
                <!-- Frequency Graph -->
                <div class="frequency-graph">
                    <div class="graph-grid"></div>
                    <svg class="graph-curve" viewBox="0 0 200 100">
                        <path d="M10,80 Q50,60 100,65 T190,70" stroke="#ff6b35" stroke-width="2" fill="none"/>
                        <path d="M10,85 Q50,75 100,78 T190,82" stroke="#666" stroke-width="1" fill="none"/>
                    </svg>
                    <div class="graph-labels">
                        <span>60Hz</span>
                        <span>500Hz</span>
                        <span>2kHz</span>
                        <span>10kHz</span>
                    </div>
                </div>
                
                <!-- EQ Sliders -->
                <div class="eq-sliders">
                    <div class="slider-group">
                        <div class="slider-track">
                            <div class="slider-handle" style="bottom: 60%"></div>
                        </div>
                        <label class="slider-label">Low</label>
                    </div>
                    <div class="slider-group">
                        <div class="slider-track">
                            <div class="slider-handle" style="bottom: 40%"></div>
                        </div>
                        <label class="slider-label">Mid</label>
                    </div>
                    <div class="slider-group">
                        <div class="slider-track">
                            <div class="slider-handle" style="bottom: 75%"></div>
                        </div>
                        <label class="slider-label">High</label>
                    </div>
                    <div class="slider-group">
                        <div class="slider-track">
                            <div class="slider-handle" style="bottom: 55%"></div>
                        </div>
                        <label class="slider-label">Air</label>
                    </div>
                </div>
                
                <!-- Speed Controls -->
                <div class="speed-controls">
                    <button class="speed-btn active">Fast</button>
                    <button class="speed-btn">Slow</button>
                    <button class="speed-btn">Auto</button>
                </div>
                
                <div class="toggle-group">
                    <label class="toggle-label">ADV</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="adv2">
                        <label for="adv2" class="toggle-slider"></label>
                    </div>
                </div>
            </section>

            <!-- Right Panel - Drive, Output, Bias, Tone, Mix -->
            <section class="control-panel panel-right">
                <header class="panel-header">
                    <button class="power-btn" aria-label="Power toggle">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                        </svg>
                    </button>
                </header>
                
                <div class="knob-row">
                    <div class="knob-control">
                        <div class="knob" data-value="65">
                            <div class="knob-indicator"></div>
                        </div>
                        <label class="control-label">DRIVE</label>
                    </div>
                    <div class="knob-control">
                        <div class="knob" data-value="80">
                            <div class="knob-indicator"></div>
                        </div>
                        <label class="control-label">OUTPUT</label>
                    </div>
                </div>
                
                <!-- Link Icon -->
                <div class="link-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5z"/>
                    </svg>
                </div>
                
                <div class="knob-row">
                    <div class="knob-control">
                        <div class="knob" data-value="50">
                            <div class="knob-indicator"></div>
                        </div>
                        <label class="control-label">BIAS</label>
                    </div>
                    <div class="knob-control">
                        <div class="knob" data-value="70">
                            <div class="knob-indicator"></div>
                        </div>
                        <label class="control-label">TONE</label>
                    </div>
                </div>
                
                <div class="control-group">
                    <div class="knob-control">
                        <div class="knob" data-value="85">
                            <div class="knob-indicator"></div>
                        </div>
                        <label class="control-label">MIX</label>
                    </div>
                </div>
                
                <div class="toggle-group">
                    <label class="toggle-label">ADV</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="adv3" checked>
                        <label for="adv3" class="toggle-slider"></label>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <script src="script.js"></script>
</body>
</html>
