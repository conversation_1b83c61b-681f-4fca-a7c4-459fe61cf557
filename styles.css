/* ===================================
   GLASSMORPHISM UI SHOWCASE
   CSS Architecture with Custom Properties
   =================================== */

/* CSS Custom Properties for Theming */
:root {
  /* Dark Theme Colors */
  --dark-bg-primary: #1a1a1a;
  --dark-bg-secondary: #2d2d2d;
  --dark-bg-gradient-start: #0f0f0f;
  --dark-bg-gradient-end: #2a2a2a;
  --dark-text-primary: #ffffff;
  --dark-text-secondary: #b0b0b0;
  --dark-text-muted: #808080;
  
  /* Light Theme Colors */
  --light-bg-primary: #f5f5f5;
  --light-bg-secondary: #ffffff;
  --light-bg-gradient-start: #fafafa;
  --light-bg-gradient-end: #e8e8e8;
  --light-text-primary: #333333;
  --light-text-secondary: #666666;
  --light-text-muted: #999999;
  
  /* Glassmorphism Properties */
  --glass-bg-dark: rgba(255, 255, 255, 0.1);
  --glass-bg-light: rgba(255, 255, 255, 0.25);
  --glass-border-dark: rgba(255, 255, 255, 0.2);
  --glass-border-light: rgba(255, 255, 255, 0.3);
  --glass-shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.3);
  --glass-shadow-light: 0 8px 32px rgba(0, 0, 0, 0.1);
  --glass-blur: 10px;
  --glass-blur-strong: 20px;
  
  /* Accent Colors */
  --accent-orange: #ff8c42;
  --accent-orange-glow: rgba(255, 140, 66, 0.4);
  --accent-blue: #4a9eff;
  --accent-blue-glow: rgba(74, 158, 255, 0.4);
  --accent-yellow: #ffd700;
  --accent-yellow-glow: rgba(255, 215, 0, 0.4);
  
  /* Audio Interface Colors */
  --audio-knob-bg: linear-gradient(145deg, #f0f0f0, #d0d0d0);
  --audio-knob-shadow: inset 2px 2px 5px rgba(0,0,0,0.1), 
                       inset -2px -2px 5px rgba(255,255,255,0.8);
  --audio-slider-track: #e0e0e0;
  --audio-slider-handle: #ffffff;
  --audio-toggle-on: #4a9eff;
  --audio-toggle-off: #cccccc;
  
  /* Layout Properties */
  --container-max-width: 1200px;
  --container-padding: 2rem;
  --border-radius-small: 8px;
  --border-radius-medium: 12px;
  --border-radius-large: 20px;
  --border-radius-round: 50%;
  
  /* Animation Properties */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Typography */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Z-index Scale */
  --z-background: -1;
  --z-base: 0;
  --z-elevated: 10;
  --z-overlay: 100;
  --z-modal: 1000;
  --z-tooltip: 10000;
}

/* ===================================
   CSS RESET AND BASE STYLES
   =================================== */

/* Modern CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--dark-text-primary);
  background: var(--dark-bg-primary);
  overflow-x: hidden;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: none;
}

/* Remove default list styles */
ul, ol {
  list-style: none;
}

/* Improve media defaults */
img, picture, video, canvas, svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* Remove built-in form typography styles */
input, button, textarea, select {
  font: inherit;
}

/* Avoid text overflows */
p, h1, h2, h3, h4, h5, h6 {
  overflow-wrap: break-word;
}

/* ===================================
   UTILITY CLASSES
   =================================== */

/* Accessibility utilities */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus utilities */
.focus-visible:focus-visible {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
}

/* Glass effect utilities */
.glass-effect {
  background: var(--glass-bg-dark);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border-dark);
  box-shadow: var(--glass-shadow-dark);
}

.glass-effect-strong {
  background: var(--glass-bg-dark);
  backdrop-filter: blur(var(--glass-blur-strong));
  -webkit-backdrop-filter: blur(var(--glass-blur-strong));
  border: 1px solid var(--glass-border-dark);
  box-shadow: var(--glass-shadow-dark);
}

/* Light theme glass effects */
.light-theme .glass-effect {
  background: var(--glass-bg-light);
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow-light);
}

.light-theme .glass-effect-strong {
  background: var(--glass-bg-light);
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow-light);
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn var(--transition-medium) ease-out;
}

.animate-slide-up {
  animation: slideUp var(--transition-medium) ease-out;
}

.animate-scale-in {
  animation: scaleIn var(--transition-bounce) ease-out;
}

/* ===================================
   KEYFRAME ANIMATIONS
   =================================== */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* ===================================
   RESPONSIVE BREAKPOINTS
   =================================== */

/* Mobile-first approach */
@media (min-width: 480px) {
  :root {
    --container-padding: 2.5rem;
  }
}

@media (min-width: 768px) {
  :root {
    --container-padding: 3rem;
    --font-size-3xl: 2.5rem;
  }
}

@media (min-width: 1024px) {
  :root {
    --container-padding: 4rem;
    --font-size-3xl: 3rem;
  }
}

@media (min-width: 1200px) {
  :root {
    --container-padding: 5rem;
  }
}

/* ===================================
   THEME TOGGLE COMPONENT
   =================================== */

.theme-toggle {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: var(--z-overlay);
}

.theme-btn {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius-round);
  background: var(--glass-bg-dark);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border-dark);
  box-shadow: var(--glass-shadow-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-medium);
  cursor: pointer;
}

.theme-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.theme-btn:active {
  transform: scale(0.95);
}

.theme-icon {
  font-size: 1.5rem;
  transition: transform var(--transition-medium);
}

.theme-btn:hover .theme-icon {
  transform: rotate(15deg);
}

/* Light theme adjustments for theme toggle */
.light-theme .theme-btn {
  background: var(--glass-bg-light);
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow-light);
}

.light-theme .theme-btn:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* ===================================
   CONTAINER AND LAYOUT
   =================================== */

.container {
  min-height: 100vh;
  padding: var(--container-padding);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* Theme-specific container styles */
.dark-theme {
  background: linear-gradient(135deg, var(--dark-bg-gradient-start), var(--dark-bg-gradient-end));
  color: var(--dark-text-primary);
}

.light-theme {
  background: linear-gradient(135deg, var(--light-bg-gradient-start), var(--light-bg-gradient-end));
  color: var(--light-text-primary);
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

@media (max-width: 767px) {
  .container {
    padding: 1rem;
  }
  
  .theme-toggle {
    top: 1rem;
    right: 1rem;
  }
  
  .theme-btn {
    width: 40px;
    height: 40px;
  }
  
  .theme-icon {
    font-size: 1.25rem;
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===================================
   RESPONSIVE LAYOUT FOUNDATION
   =================================== */

/* Dark Theme - Mobile App Interface Layout */
.dark-theme .main-card {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  position: relative;
  z-index: var(--z-elevated);
}

/* Light Theme - Audio Interface Layout */
.audio-interface {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  width: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.control-panel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 2rem;
  min-height: 500px;
}

/* Panel-specific layouts */
.panel-left,
.panel-right {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.panel-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

/* Control group layouts */
.control-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.knob-row {
  display: flex;
  gap: 2rem;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.knob-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

/* EQ Sliders Layout */
.eq-sliders {
  display: flex;
  gap: 1rem;
  align-items: end;
  justify-content: center;
  height: 120px;
  margin: 1rem 0;
}

.slider-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  height: 100%;
}

/* Speed Controls Layout */
.speed-controls {
  display: flex;
  gap: 0.5rem;
  margin: 1rem 0;
}

/* Toggle Group Layout */
.toggle-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin-top: auto;
}

/* ===================================
   RESPONSIVE GRID ADJUSTMENTS
   =================================== */

/* Tablet Layout */
@media (min-width: 768px) and (max-width: 1023px) {
  .audio-interface {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .panel-center {
    grid-column: 1 / -1;
  }

  .dark-theme .main-card {
    max-width: 450px;
  }
}

/* Desktop Layout */
@media (min-width: 1024px) {
  .audio-interface {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .dark-theme .main-card {
    max-width: 500px;
  }

  .knob-row {
    gap: 3rem;
  }
}

/* Large Desktop Layout */
@media (min-width: 1200px) {
  .audio-interface {
    gap: 2.5rem;
  }

  .control-panel {
    padding: 2.5rem;
    min-height: 550px;
  }
}

/* ===================================
   MOBILE-SPECIFIC LAYOUTS
   =================================== */

@media (max-width: 767px) {
  .audio-interface {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .control-panel {
    padding: 1.5rem;
    min-height: 400px;
  }

  .knob-row {
    gap: 1.5rem;
  }

  .eq-sliders {
    height: 100px;
    gap: 0.75rem;
  }

  .dark-theme .main-card {
    max-width: 100%;
    margin: 0;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .control-panel {
    padding: 1rem;
    min-height: 350px;
  }

  .knob-row {
    gap: 1rem;
  }

  .eq-sliders {
    height: 80px;
    gap: 0.5rem;
  }
}

/* ===================================
   FLEXBOX UTILITIES FOR COMPONENTS
   =================================== */

/* Card header layout */
.card-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

/* Social icons layout */
.social-icons {
  display: flex;
  gap: 1rem;
  justify-content: flex-start;
}

/* Geometric pattern layout */
.geometric-pattern {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 200px;
  position: relative;
}

.pattern-container {
  position: relative;
  width: 150px;
  height: 150px;
}

/* Card content layout */
.card-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  text-align: center;
  width: 100%;
}

/* Panel header layout */
.panel-header {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: 1rem;
}

/* Frequency graph layout */
.frequency-graph {
  position: relative;
  width: 100%;
  height: 80px;
  margin: 1rem 0;
}

.graph-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: var(--font-size-xs);
  color: var(--light-text-muted);
}

/* Link icon layout */
.link-icon {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
  opacity: 0.6;
}

/* ===================================
   LAYOUT ANIMATIONS
   =================================== */

/* Staggered animation for control panels */
.control-panel:nth-child(1) {
  animation-delay: 0.1s;
}

.control-panel:nth-child(2) {
  animation-delay: 0.2s;
}

.control-panel:nth-child(3) {
  animation-delay: 0.3s;
}

/* Responsive animation adjustments */
@media (prefers-reduced-motion: reduce) {
  .control-panel {
    animation: none;
  }
}

/* ===================================
   BACKGROUND ATMOSPHERES
   =================================== */

/* Dark Theme Background Atmosphere */
.background-atmosphere {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: var(--z-background);
  overflow: hidden;
  pointer-events: none;
}

/* Gradient Orbs for Dark Theme */
.gradient-orb {
  position: absolute;
  border-radius: var(--border-radius-round);
  filter: blur(60px);
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, var(--accent-orange) 0%, transparent 70%);
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, var(--accent-blue) 0%, transparent 70%);
  top: 60%;
  right: -5%;
  animation-delay: 2s;
}

.orb-3 {
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, var(--accent-yellow) 0%, transparent 70%);
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

/* Enhanced Dark Theme Background */
.dark-theme::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(ellipse at 20% 80%, rgba(255, 140, 66, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 20%, rgba(74, 158, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 40% 40%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
  z-index: var(--z-background);
  pointer-events: none;
}

/* Light Theme Background Atmosphere */
.light-theme::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(ellipse at 30% 70%, rgba(74, 158, 255, 0.03) 0%, transparent 50%),
    radial-gradient(ellipse at 70% 30%, rgba(255, 140, 66, 0.02) 0%, transparent 50%),
    linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(240, 240, 240, 0.05) 100%);
  z-index: var(--z-background);
  pointer-events: none;
}

/* Subtle texture overlay for light theme */
.light-theme::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 1px 1px, rgba(0,0,0,0.02) 1px, transparent 0);
  background-size: 20px 20px;
  z-index: var(--z-background);
  pointer-events: none;
  opacity: 0.3;
}

/* Top Light Bar for Dark Theme */
.top-light-bar {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 4px;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--accent-orange) 20%,
    var(--accent-orange) 80%,
    transparent 100%);
  border-radius: 2px;
  box-shadow:
    0 0 20px var(--accent-orange-glow),
    0 0 40px var(--accent-orange-glow),
    0 0 60px var(--accent-orange-glow);
  z-index: var(--z-elevated);
  animation: glow 3s ease-in-out infinite;
}

/* Side Glass Elements for Dark Theme */
.side-glass-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: var(--z-base);
  pointer-events: none;
}

.side-glass {
  position: absolute;
  width: 100px;
  height: 200px;
  background: var(--glass-bg-dark);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid var(--glass-border-dark);
  border-radius: var(--border-radius-large);
  opacity: 0.3;
}

.left-glass {
  top: 30%;
  left: -50px;
  transform: rotate(-15deg);
  animation: float 8s ease-in-out infinite;
}

.right-glass {
  top: 50%;
  right: -50px;
  transform: rotate(15deg);
  animation: float 8s ease-in-out infinite reverse;
}

/* ===================================
   RESPONSIVE BACKGROUND ADJUSTMENTS
   =================================== */

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .orb-1 {
    width: 350px;
    height: 350px;
  }

  .orb-2 {
    width: 300px;
    height: 300px;
  }

  .top-light-bar {
    width: 400px;
  }
}

/* Desktop adjustments */
@media (min-width: 1024px) {
  .orb-1 {
    width: 400px;
    height: 400px;
  }

  .orb-2 {
    width: 350px;
    height: 350px;
  }

  .orb-3 {
    width: 250px;
    height: 250px;
  }

  .top-light-bar {
    width: 500px;
  }

  .side-glass {
    width: 120px;
    height: 250px;
  }
}

/* Mobile adjustments */
@media (max-width: 767px) {
  .orb-1 {
    width: 200px;
    height: 200px;
  }

  .orb-2 {
    width: 180px;
    height: 180px;
  }

  .orb-3 {
    width: 150px;
    height: 150px;
  }

  .top-light-bar {
    width: 250px;
  }

  .side-glass {
    width: 80px;
    height: 150px;
  }

  .left-glass {
    left: -40px;
  }

  .right-glass {
    right: -40px;
  }
}

/* Performance optimizations for reduced motion */
@media (prefers-reduced-motion: reduce) {
  .gradient-orb,
  .top-light-bar,
  .side-glass {
    animation: none;
  }

  .background-atmosphere {
    filter: none;
  }
}

/* ===================================
   BACKDROP-FILTER EFFECTS & CROSS-BROWSER COMPATIBILITY
   =================================== */

/* Core Glassmorphism Mixin Classes */
.glass-base {
  /* Fallback for browsers without backdrop-filter support */
  background: rgba(255, 255, 255, 0.1);

  /* Modern browsers with backdrop-filter support */
  backdrop-filter: blur(10px) saturate(180%);
  -webkit-backdrop-filter: blur(10px) saturate(180%);

  /* Enhanced glass effect for supported browsers */
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Feature detection for backdrop-filter support */
@supports (backdrop-filter: blur(1px)) {
  .glass-base {
    background: rgba(255, 255, 255, 0.08);
  }
}

@supports (-webkit-backdrop-filter: blur(1px)) {
  .glass-base {
    background: rgba(255, 255, 255, 0.08);
  }
}

/* Stronger glass effect for key elements */
.glass-strong {
  /* Fallback */
  background: rgba(255, 255, 255, 0.15);

  /* Modern browsers */
  backdrop-filter: blur(20px) saturate(200%);
  -webkit-backdrop-filter: blur(20px) saturate(200%);

  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

@supports (backdrop-filter: blur(1px)) {
  .glass-strong {
    background: rgba(255, 255, 255, 0.1);
  }
}

/* Subtle glass effect for secondary elements */
.glass-subtle {
  /* Fallback */
  background: rgba(255, 255, 255, 0.05);

  /* Modern browsers */
  backdrop-filter: blur(5px) saturate(150%);
  -webkit-backdrop-filter: blur(5px) saturate(150%);

  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

@supports (backdrop-filter: blur(1px)) {
  .glass-subtle {
    background: rgba(255, 255, 255, 0.03);
  }
}

/* Light theme glass adjustments */
.light-theme .glass-base {
  /* Fallback for light theme */
  background: rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

@supports (backdrop-filter: blur(1px)) {
  .light-theme .glass-base {
    background: rgba(255, 255, 255, 0.25);
  }
}

.light-theme .glass-strong {
  /* Fallback for light theme */
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.7);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

@supports (backdrop-filter: blur(1px)) {
  .light-theme .glass-strong {
    background: rgba(255, 255, 255, 0.4);
  }
}

.light-theme .glass-subtle {
  /* Fallback for light theme */
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

@supports (backdrop-filter: blur(1px)) {
  .light-theme .glass-subtle {
    background: rgba(255, 255, 255, 0.2);
  }
}

/* ===================================
   BROWSER-SPECIFIC OPTIMIZATIONS
   =================================== */

/* Firefox-specific optimizations */
@-moz-document url-prefix() {
  .glass-base,
  .glass-strong,
  .glass-subtle {
    /* Firefox fallback - uses regular transparency */
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background: rgba(255, 255, 255, 0.15);
  }

  .light-theme .glass-base,
  .light-theme .glass-strong,
  .light-theme .glass-subtle {
    background: rgba(255, 255, 255, 0.5);
  }
}

/* Safari-specific optimizations */
@supports (-webkit-backdrop-filter: blur(1px)) and (not (backdrop-filter: blur(1px))) {
  .glass-base,
  .glass-strong,
  .glass-subtle {
    -webkit-backdrop-filter: blur(10px) saturate(180%);
  }
}

/* Edge Legacy fallback */
@supports not (backdrop-filter: blur(1px)) and not (-webkit-backdrop-filter: blur(1px)) {
  .glass-base {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  .glass-strong {
    background: rgba(255, 255, 255, 0.25);
    border: 2px solid rgba(255, 255, 255, 0.4);
  }

  .glass-subtle {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .light-theme .glass-base {
    background: rgba(255, 255, 255, 0.6);
  }

  .light-theme .glass-strong {
    background: rgba(255, 255, 255, 0.7);
  }

  .light-theme .glass-subtle {
    background: rgba(255, 255, 255, 0.5);
  }
}

/* ===================================
   PERFORMANCE OPTIMIZATIONS
   =================================== */

/* GPU acceleration for glass elements */
.glass-base,
.glass-strong,
.glass-subtle {
  transform: translateZ(0);
  will-change: backdrop-filter;
}

/* Reduce blur on mobile for performance */
@media (max-width: 767px) {
  .glass-base {
    backdrop-filter: blur(8px) saturate(150%);
    -webkit-backdrop-filter: blur(8px) saturate(150%);
  }

  .glass-strong {
    backdrop-filter: blur(15px) saturate(180%);
    -webkit-backdrop-filter: blur(15px) saturate(180%);
  }

  .glass-subtle {
    backdrop-filter: blur(3px) saturate(120%);
    -webkit-backdrop-filter: blur(3px) saturate(120%);
  }
}

/* High-performance mode for low-end devices */
@media (max-width: 480px) and (max-resolution: 150dpi) {
  .glass-base,
  .glass-strong,
  .glass-subtle {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background: rgba(255, 255, 255, 0.2);
  }

  .light-theme .glass-base,
  .light-theme .glass-strong,
  .light-theme .glass-subtle {
    background: rgba(255, 255, 255, 0.6);
  }
}

/* ===================================
   GLASS CARD CONTAINERS
   =================================== */

/* Main Glass Card for Dark Theme */
.glass-card {
  @extend .glass-strong;
  border-radius: var(--border-radius-large);
  padding: 2rem;
  position: relative;
  overflow: hidden;
  transition: all var(--transition-medium);
}

/* Alternative approach without @extend for better compatibility */
.glass-card {
  /* Fallback */
  background: rgba(255, 255, 255, 0.15);

  /* Modern browsers */
  backdrop-filter: blur(20px) saturate(200%);
  -webkit-backdrop-filter: blur(20px) saturate(200%);

  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-large);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  padding: 2rem;
  position: relative;
  overflow: hidden;
  transition: all var(--transition-medium);
}

@supports (backdrop-filter: blur(1px)) {
  .glass-card {
    background: rgba(255, 255, 255, 0.1);
  }
}

/* Main card hover effect */
.glass-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 16px 50px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

/* Light theme glass card adjustments */
.light-theme .glass-card {
  /* Fallback for light theme */
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.7);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

@supports (backdrop-filter: blur(1px)) {
  .light-theme .glass-card {
    background: rgba(255, 255, 255, 0.4);
  }
}

.light-theme .glass-card:hover {
  box-shadow:
    0 16px 50px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.95),
    inset 0 -1px 0 rgba(0, 0, 0, 0.08);
}

/* Control Panel Glass Containers for Light Theme */
.control-panel {
  /* Fallback */
  background: rgba(255, 255, 255, 0.4);

  /* Modern browsers */
  backdrop-filter: blur(10px) saturate(180%);
  -webkit-backdrop-filter: blur(10px) saturate(180%);

  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: var(--border-radius-medium);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-medium);
}

@supports (backdrop-filter: blur(1px)) {
  .control-panel {
    background: rgba(255, 255, 255, 0.25);
  }
}

/* Control panel hover effect */
.control-panel:hover {
  transform: translateY(-1px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.85);
}

/* Panel-specific glass variations */
.panel-left,
.panel-right {
  /* Slightly more transparent for side panels */
  background: rgba(255, 255, 255, 0.35);
}

@supports (backdrop-filter: blur(1px)) {
  .panel-left,
  .panel-right {
    background: rgba(255, 255, 255, 0.2);
  }
}

.panel-center {
  /* Stronger glass effect for center panel */
  background: rgba(255, 255, 255, 0.45);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

@supports (backdrop-filter: blur(1px)) {
  .panel-center {
    background: rgba(255, 255, 255, 0.3);
  }
}

/* ===================================
   GLASS CONTAINER ANIMATIONS
   =================================== */

/* Entrance animations */
.glass-card {
  animation: scaleIn var(--transition-bounce) ease-out;
}

.control-panel {
  animation: slideUp var(--transition-medium) ease-out;
}

/* Staggered entrance for control panels */
.control-panel:nth-child(1) {
  animation-delay: 0.1s;
}

.control-panel:nth-child(2) {
  animation-delay: 0.2s;
}

.control-panel:nth-child(3) {
  animation-delay: 0.3s;
}

/* ===================================
   RESPONSIVE GLASS CONTAINERS
   =================================== */

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .glass-card {
    padding: 2.5rem;
  }

  .control-panel {
    padding: 2rem;
  }
}

/* Desktop adjustments */
@media (min-width: 1024px) {
  .glass-card {
    padding: 3rem;
  }

  .control-panel {
    padding: 2.5rem;
  }
}

/* Mobile adjustments */
@media (max-width: 767px) {
  .glass-card {
    padding: 1.5rem;
    border-radius: var(--border-radius-medium);
  }

  .control-panel {
    padding: 1.5rem;
    border-radius: var(--border-radius-small);
  }

  /* Reduce blur for performance on mobile */
  .glass-card {
    backdrop-filter: blur(15px) saturate(180%);
    -webkit-backdrop-filter: blur(15px) saturate(180%);
  }

  .control-panel {
    backdrop-filter: blur(8px) saturate(150%);
    -webkit-backdrop-filter: blur(8px) saturate(150%);
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .glass-card {
    padding: 1rem;
  }

  .control-panel {
    padding: 1rem;
  }
}

/* ===================================
   BORDER AND SHADOW SYSTEMS FOR DEPTH
   =================================== */

/* Layered shadow system for depth hierarchy */
.depth-1 {
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.08);
}

.depth-2 {
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.15),
    0 2px 6px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.depth-3 {
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

.depth-4 {
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.25),
    0 6px 20px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.depth-5 {
  box-shadow:
    0 16px 50px rgba(0, 0, 0, 0.3),
    0 8px 25px rgba(0, 0, 0, 0.25),
    inset 0 2px 0 rgba(255, 255, 255, 0.25),
    inset 0 -2px 0 rgba(0, 0, 0, 0.15);
}

/* Light theme shadow adjustments */
.light-theme .depth-1 {
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.04);
}

.light-theme .depth-2 {
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.08),
    0 2px 6px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.light-theme .depth-3 {
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);
}

.light-theme .depth-4 {
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.12),
    0 6px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.95),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

.light-theme .depth-5 {
  box-shadow:
    0 16px 50px rgba(0, 0, 0, 0.15),
    0 8px 25px rgba(0, 0, 0, 0.12),
    inset 0 2px 0 rgba(255, 255, 255, 1),
    inset 0 -2px 0 rgba(0, 0, 0, 0.08);
}

/* ===================================
   BORDER SYSTEMS
   =================================== */

/* Glass border variations */
.border-glass-subtle {
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.border-glass-medium {
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.border-glass-strong {
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.border-glass-accent {
  border: 1px solid rgba(255, 255, 255, 0.4);
}

/* Light theme border adjustments */
.light-theme .border-glass-subtle {
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.light-theme .border-glass-medium {
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.light-theme .border-glass-strong {
  border: 1px solid rgba(255, 255, 255, 0.7);
}

.light-theme .border-glass-accent {
  border: 1px solid rgba(255, 255, 255, 0.9);
}

/* Gradient borders for special elements */
.border-gradient-orange {
  border: 1px solid transparent;
  background:
    linear-gradient(var(--dark-bg-primary), var(--dark-bg-primary)) padding-box,
    linear-gradient(45deg, var(--accent-orange), rgba(255, 140, 66, 0.3)) border-box;
}

.border-gradient-blue {
  border: 1px solid transparent;
  background:
    linear-gradient(var(--dark-bg-primary), var(--dark-bg-primary)) padding-box,
    linear-gradient(45deg, var(--accent-blue), rgba(74, 158, 255, 0.3)) border-box;
}

/* ===================================
   COMPONENT-SPECIFIC DEPTH APPLICATIONS
   =================================== */

/* Apply depth to main components */
.glass-card {
  @extend .depth-4;
}

.control-panel {
  @extend .depth-3;
}

/* Alternative without @extend */
.glass-card {
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 6px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.control-panel {
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);
}

/* Interactive depth changes */
.glass-card:hover {
  box-shadow:
    0 16px 50px rgba(0, 0, 0, 0.5),
    0 8px 25px rgba(0, 0, 0, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.2),
    inset 0 -2px 0 rgba(0, 0, 0, 0.15);
}

.control-panel:hover {
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 6px 20px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.85),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

/* Button depth states */
.cta-button {
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 2px 6px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.cta-button:hover {
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.25),
    0 3px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.cta-button:active {
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Knob depth */
.knob {
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 2px 4px rgba(255, 255, 255, 0.8),
    inset 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.knob:active {
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.15),
    inset 0 2px 4px rgba(0, 0, 0, 0.15),
    inset 0 -1px 2px rgba(255, 255, 255, 0.6);
}

/* ===================================
   RESPONSIVE DEPTH ADJUSTMENTS
   =================================== */

/* Reduce shadows on mobile for performance */
@media (max-width: 767px) {
  .glass-card {
    box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.3),
      0 4px 10px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .control-panel {
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.08),
      0 2px 6px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.7);
  }

  .cta-button {
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.15),
      0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .knob {
    box-shadow:
      0 2px 6px rgba(0, 0, 0, 0.1),
      inset 0 1px 2px rgba(255, 255, 255, 0.6),
      inset 0 -1px 2px rgba(0, 0, 0, 0.08);
  }
}

/* ===================================
   GRADIENT OVERLAYS AND LIGHTING EFFECTS
   =================================== */

/* Glass surface lighting gradients */
.glass-lighting::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    transparent 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

.glass-lighting::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30%;
  background: linear-gradient(0deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.03) 50%,
    transparent 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

/* Light theme lighting adjustments */
.light-theme .glass-lighting::before {
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%);
}

.light-theme .glass-lighting::after {
  background: linear-gradient(0deg,
    rgba(0, 0, 0, 0.05) 0%,
    rgba(0, 0, 0, 0.02) 50%,
    transparent 100%);
}

/* Accent lighting effects */
.accent-glow-orange {
  position: relative;
}

.accent-glow-orange::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    var(--accent-orange-glow) 0%,
    transparent 50%,
    var(--accent-orange-glow) 100%);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.accent-glow-orange:hover::before {
  opacity: 1;
}

.accent-glow-blue {
  position: relative;
}

.accent-glow-blue::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    var(--accent-blue-glow) 0%,
    transparent 50%,
    var(--accent-blue-glow) 100%);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.accent-glow-blue:hover::before {
  opacity: 1;
}

/* Shimmer effect for interactive elements */
.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%);
  transition: left 0.6s ease;
  z-index: 2;
}

.shimmer-effect:hover::before {
  left: 100%;
}

/* Reflection effects for glass surfaces */
.glass-reflection {
  position: relative;
}

.glass-reflection::before {
  content: '';
  position: absolute;
  top: 10%;
  left: 10%;
  width: 30%;
  height: 40%;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%);
  border-radius: 50% 20% 50% 20%;
  filter: blur(1px);
  pointer-events: none;
  z-index: 1;
}

/* Light theme reflection adjustments */
.light-theme .glass-reflection::before {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%);
}

/* Ambient lighting for containers */
.ambient-light-warm {
  position: relative;
}

.ambient-light-warm::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle,
    rgba(255, 140, 66, 0.1) 0%,
    rgba(255, 140, 66, 0.05) 30%,
    transparent 70%);
  transform: translate(-50%, -50%);
  z-index: -1;
  pointer-events: none;
}

.ambient-light-cool {
  position: relative;
}

.ambient-light-cool::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle,
    rgba(74, 158, 255, 0.1) 0%,
    rgba(74, 158, 255, 0.05) 30%,
    transparent 70%);
  transform: translate(-50%, -50%);
  z-index: -1;
  pointer-events: none;
}

/* ===================================
   COMPONENT-SPECIFIC LIGHTING
   =================================== */

/* Apply lighting to main glass card */
.glass-card {
  @extend .glass-lighting;
  @extend .glass-reflection;
  @extend .ambient-light-warm;
}

/* Alternative without @extend */
.glass-card {
  position: relative;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    transparent 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

.glass-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30%;
  background: linear-gradient(0deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.03) 50%,
    transparent 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

/* Control panel lighting */
.control-panel {
  position: relative;
}

.control-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40%;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

/* Button lighting effects */
.cta-button {
  position: relative;
  overflow: hidden;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

/* Knob lighting */
.knob {
  position: relative;
}

.knob::before {
  content: '';
  position: absolute;
  top: 15%;
  left: 15%;
  width: 30%;
  height: 30%;
  background: radial-gradient(circle,
    rgba(255, 255, 255, 0.6) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 1;
}

/* ===================================
   GLOWING ORANGE LIGHT BAR
   =================================== */

/* Main light bar styling */
.top-light-bar {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 4px;
  border-radius: 2px;
  z-index: var(--z-elevated);

  /* Core gradient */
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 140, 66, 0.3) 10%,
    var(--accent-orange) 20%,
    #ffaa44 40%,
    #ff8c42 50%,
    #ffaa44 60%,
    var(--accent-orange) 80%,
    rgba(255, 140, 66, 0.3) 90%,
    transparent 100%);

  /* Multi-layered glow effect */
  box-shadow:
    /* Inner glow */
    inset 0 0 4px rgba(255, 200, 100, 0.8),
    /* Close glow */
    0 0 8px var(--accent-orange-glow),
    0 0 16px var(--accent-orange-glow),
    /* Medium glow */
    0 0 24px rgba(255, 140, 66, 0.6),
    0 0 32px rgba(255, 140, 66, 0.4),
    /* Far glow */
    0 0 48px rgba(255, 140, 66, 0.3),
    0 0 64px rgba(255, 140, 66, 0.2);

  /* Pulsing animation */
  animation: lightBarGlow 3s ease-in-out infinite;
}

/* Enhanced glow animation */
@keyframes lightBarGlow {
  0%, 100% {
    opacity: 0.8;
    box-shadow:
      inset 0 0 4px rgba(255, 200, 100, 0.8),
      0 0 8px var(--accent-orange-glow),
      0 0 16px var(--accent-orange-glow),
      0 0 24px rgba(255, 140, 66, 0.6),
      0 0 32px rgba(255, 140, 66, 0.4),
      0 0 48px rgba(255, 140, 66, 0.3),
      0 0 64px rgba(255, 140, 66, 0.2);
  }
  50% {
    opacity: 1;
    box-shadow:
      inset 0 0 6px rgba(255, 200, 100, 1),
      0 0 12px var(--accent-orange-glow),
      0 0 20px var(--accent-orange-glow),
      0 0 30px rgba(255, 140, 66, 0.8),
      0 0 40px rgba(255, 140, 66, 0.6),
      0 0 60px rgba(255, 140, 66, 0.4),
      0 0 80px rgba(255, 140, 66, 0.3);
  }
}

/* Light bar reflection effect */
.top-light-bar::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 20%;
  width: 60%;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 30%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0.6) 70%,
    transparent 100%);
  border-radius: 1px;
  filter: blur(0.5px);
}

/* Light bar ambient lighting */
.top-light-bar::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 400px;
  height: 100px;
  background: radial-gradient(ellipse,
    rgba(255, 140, 66, 0.15) 0%,
    rgba(255, 140, 66, 0.08) 30%,
    rgba(255, 140, 66, 0.04) 50%,
    transparent 70%);
  transform: translate(-50%, -50%);
  z-index: -1;
  pointer-events: none;
  animation: ambientPulse 4s ease-in-out infinite;
}

@keyframes ambientPulse {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* ===================================
   RESPONSIVE LIGHT BAR
   =================================== */

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .top-light-bar {
    width: 400px;
    height: 5px;
  }

  .top-light-bar::after {
    width: 500px;
    height: 120px;
  }
}

/* Desktop adjustments */
@media (min-width: 1024px) {
  .top-light-bar {
    width: 500px;
    height: 6px;
  }

  .top-light-bar::after {
    width: 600px;
    height: 140px;
  }
}

/* Mobile adjustments */
@media (max-width: 767px) {
  .top-light-bar {
    width: 250px;
    height: 3px;

    /* Reduced glow for performance */
    box-shadow:
      inset 0 0 3px rgba(255, 200, 100, 0.8),
      0 0 6px var(--accent-orange-glow),
      0 0 12px var(--accent-orange-glow),
      0 0 18px rgba(255, 140, 66, 0.5),
      0 0 24px rgba(255, 140, 66, 0.3);
  }

  .top-light-bar::after {
    width: 300px;
    height: 80px;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .top-light-bar {
    width: 200px;
    height: 2px;

    /* Minimal glow for low-end devices */
    box-shadow:
      inset 0 0 2px rgba(255, 200, 100, 0.8),
      0 0 4px var(--accent-orange-glow),
      0 0 8px var(--accent-orange-glow),
      0 0 12px rgba(255, 140, 66, 0.4);
  }

  .top-light-bar::after {
    width: 250px;
    height: 60px;
  }
}

/* Performance optimizations */
@media (prefers-reduced-motion: reduce) {
  .top-light-bar {
    animation: none;
  }

  .top-light-bar::after {
    animation: none;
  }
}

/* High contrast mode adjustments */
@media (prefers-contrast: high) {
  .top-light-bar {
    background: linear-gradient(90deg,
      transparent 0%,
      #ff8c42 20%,
      #ffaa44 50%,
      #ff8c42 80%,
      transparent 100%);

    box-shadow:
      0 0 4px #ff8c42,
      0 0 8px #ff8c42;
  }
}

/* ===================================
   SOCIAL MEDIA ICONS AND NAVIGATION
   =================================== */

/* Social icons container */
.social-icons {
  display: flex;
  gap: 1rem;
  justify-content: flex-start;
  align-items: center;
  margin: 1rem 0;
}

/* Individual social icon styling */
.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-small);
  color: var(--dark-text-secondary);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

@supports (backdrop-filter: blur(1px)) {
  .social-icon {
    background: rgba(255, 255, 255, 0.03);
  }
}

/* Social icon hover effects */
.social-icon:hover {
  color: var(--dark-text-primary);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.3),
    0 2px 6px rgba(0, 0, 0, 0.2);
}

@supports (backdrop-filter: blur(1px)) {
  .social-icon:hover {
    background: rgba(255, 255, 255, 0.08);
  }
}

/* Social icon active state */
.social-icon:active {
  transform: translateY(0);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.4),
    0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Social icon focus state for accessibility */
.social-icon:focus-visible {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
}

/* Platform-specific hover colors */
.social-icon:nth-child(1):hover {
  /* Instagram gradient */
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: white;
}

.social-icon:nth-child(2):hover {
  /* Twitter blue */
  background: #1da1f2;
  color: white;
}

.social-icon:nth-child(3):hover {
  /* LinkedIn blue */
  background: #0077b5;
  color: white;
}

/* Social icon shimmer effect */
.social-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%);
  transition: left 0.5s ease;
  z-index: 1;
}

.social-icon:hover::before {
  left: 100%;
}

/* SVG icon styling */
.social-icon svg {
  width: 20px;
  height: 20px;
  fill: currentColor;
  transition: transform var(--transition-medium);
  z-index: 2;
  position: relative;
}

.social-icon:hover svg {
  transform: scale(1.1);
}

/* ===================================
   CARD HEADER STYLING
   =================================== */

.card-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  margin-bottom: 2rem;
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--dark-text-secondary);
  line-height: 1.5;
  margin: 0;
  opacity: 0.8;
}

/* ===================================
   RESPONSIVE SOCIAL ICONS
   =================================== */

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .social-icons {
    gap: 1.25rem;
  }

  .social-icon {
    width: 44px;
    height: 44px;
  }

  .social-icon svg {
    width: 22px;
    height: 22px;
  }
}

/* Desktop adjustments */
@media (min-width: 1024px) {
  .social-icons {
    gap: 1.5rem;
  }

  .social-icon {
    width: 48px;
    height: 48px;
  }

  .social-icon svg {
    width: 24px;
    height: 24px;
  }
}

/* Mobile adjustments */
@media (max-width: 767px) {
  .social-icons {
    gap: 0.75rem;
    justify-content: center;
  }

  .social-icon {
    width: 36px;
    height: 36px;
  }

  .social-icon svg {
    width: 18px;
    height: 18px;
  }

  .card-subtitle {
    text-align: center;
    font-size: var(--font-size-xs);
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .social-icons {
    gap: 0.5rem;
  }

  .social-icon {
    width: 32px;
    height: 32px;
  }

  .social-icon svg {
    width: 16px;
    height: 16px;
  }
}

/* ===================================
   ACCESSIBILITY ENHANCEMENTS
   =================================== */

/* High contrast mode */
@media (prefers-contrast: high) {
  .social-icon {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.5);
  }

  .social-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.8);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .social-icon {
    transition: none;
  }

  .social-icon::before {
    display: none;
  }

  .social-icon svg {
    transition: none;
  }

  .social-icon:hover svg {
    transform: none;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .social-icon {
    width: 44px;
    height: 44px;
  }

  .social-icon:hover {
    transform: none;
  }

  .social-icon:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.15);
  }
}

/* ===================================
   GEOMETRIC PATTERN WITH CIRCLES AND LINES
   =================================== */

/* Pattern container */
.geometric-pattern {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 200px;
  position: relative;
  margin: 2rem 0;
}

.pattern-container {
  position: relative;
  width: 150px;
  height: 150px;
}

/* Circle group container */
.circle-group {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Individual circles */
.circle {
  position: absolute;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  transition: all var(--transition-medium);
}

@supports (backdrop-filter: blur(1px)) {
  .circle {
    background: rgba(255, 255, 255, 0.03);
  }
}

/* Circle positions and sizes */
.circle-1 {
  width: 60px;
  height: 60px;
  top: 10px;
  left: 45px;
  animation: float 4s ease-in-out infinite;
}

.circle-2 {
  width: 40px;
  height: 40px;
  top: 40px;
  left: 20px;
  animation: float 4s ease-in-out infinite 1s;
}

.circle-3 {
  width: 50px;
  height: 50px;
  top: 80px;
  left: 50px;
  animation: float 4s ease-in-out infinite 2s;
}

.circle-4 {
  width: 35px;
  height: 35px;
  top: 60px;
  left: 90px;
  animation: float 4s ease-in-out infinite 3s;
}

/* Circle hover effects */
.circle:hover {
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* ===================================
   CONNECTING LINES
   =================================== */

.connecting-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.line {
  position: absolute;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 20%,
    rgba(255, 255, 255, 0.5) 50%,
    rgba(255, 255, 255, 0.3) 80%,
    transparent 100%);
  border-radius: 1px;
  opacity: 0.6;
  animation: lineGlow 3s ease-in-out infinite;
}

/* Line 1: Connects circle-1 to circle-2 */
.line-1 {
  width: 40px;
  height: 1px;
  top: 45px;
  left: 50px;
  transform: rotate(-30deg);
  animation-delay: 0.5s;
}

/* Line 2: Connects circle-2 to circle-3 */
.line-2 {
  width: 45px;
  height: 1px;
  top: 70px;
  left: 40px;
  transform: rotate(45deg);
  animation-delay: 1.5s;
}

/* Line 3: Connects circle-3 to circle-4 */
.line-3 {
  width: 50px;
  height: 1px;
  top: 85px;
  left: 70px;
  transform: rotate(-20deg);
  animation-delay: 2.5s;
}

@keyframes lineGlow {
  0%, 100% {
    opacity: 0.4;
    box-shadow: 0 0 2px rgba(255, 255, 255, 0.3);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
  }
}

/* ===================================
   ACCENT DOTS
   =================================== */

.accent-dots {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.dot {
  position: absolute;
  border-radius: 50%;
  background: var(--accent-blue);
  box-shadow:
    0 0 8px var(--accent-blue-glow),
    0 0 16px var(--accent-blue-glow);
  animation: dotPulse 2s ease-in-out infinite;
}

.dot-1 {
  width: 8px;
  height: 8px;
  top: 30px;
  right: 20px;
  animation-delay: 0.3s;
}

.dot-2 {
  width: 6px;
  height: 6px;
  bottom: 40px;
  left: 30px;
  animation-delay: 1.3s;
}

@keyframes dotPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* ===================================
   YELLOW ACCENT ELEMENT
   =================================== */

.yellow-accent {
  position: absolute;
  bottom: 20px;
  right: 15px;
  width: 20px;
  height: 12px;
  background: var(--accent-yellow);
  border-radius: 2px;
  box-shadow:
    0 0 6px var(--accent-yellow-glow),
    0 0 12px var(--accent-yellow-glow);
  animation: yellowGlow 2.5s ease-in-out infinite;
}

.yellow-accent::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 1px;
}

@keyframes yellowGlow {
  0%, 100% {
    opacity: 0.8;
    box-shadow:
      0 0 6px var(--accent-yellow-glow),
      0 0 12px var(--accent-yellow-glow);
  }
  50% {
    opacity: 1;
    box-shadow:
      0 0 10px var(--accent-yellow-glow),
      0 0 20px var(--accent-yellow-glow);
  }
}

/* ===================================
   TYPOGRAPHY HIERARCHY
   =================================== */

/* Main heading */
.main-heading {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--dark-text-primary);
  line-height: 1.2;
  margin: 0 0 1rem 0;
  text-align: center;
  letter-spacing: -0.02em;

  /* Subtle text shadow for depth */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Main description */
.main-description {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  color: var(--dark-text-secondary);
  line-height: 1.6;
  margin: 0 0 2rem 0;
  text-align: center;
  opacity: 0.9;
}

/* Card subtitle */
.card-subtitle {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  color: var(--dark-text-muted);
  line-height: 1.5;
  margin: 0 0 1rem 0;
  opacity: 0.8;
}

/* Control labels */
.control-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--light-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  text-align: center;
  margin-top: 0.5rem;
}

.slider-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--light-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  text-align: center;
}

.toggle-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--light-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  text-align: center;
  margin-bottom: 0.5rem;
}

/* ===================================
   CALL-TO-ACTION BUTTON
   =================================== */

.cta-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--dark-text-primary);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-medium);
  cursor: pointer;
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
  min-width: 140px;

  /* Button depth */
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 2px 6px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

@supports (backdrop-filter: blur(1px)) {
  .cta-button {
    background: rgba(255, 255, 255, 0.08);
  }
}

/* Button lighting effect */
.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

/* Button hover state */
.cta-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.25),
    0 3px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

@supports (backdrop-filter: blur(1px)) {
  .cta-button:hover {
    background: rgba(255, 255, 255, 0.12);
  }
}

/* Button active state */
.cta-button:active {
  transform: translateY(0);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Button focus state */
.cta-button:focus-visible {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
}

/* Button shimmer effect */
.cta-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%);
  transition: left 0.6s ease;
  z-index: 2;
}

.cta-button:hover::after {
  left: 100%;
}

/* Button text */
.cta-button span {
  position: relative;
  z-index: 3;
}

/* ===================================
   RESPONSIVE TYPOGRAPHY
   =================================== */

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .main-heading {
    font-size: 2.25rem;
  }

  .main-description {
    font-size: var(--font-size-xl);
  }

  .cta-button {
    padding: 1.125rem 2.25rem;
    font-size: var(--font-size-lg);
  }
}

/* Desktop adjustments */
@media (min-width: 1024px) {
  .main-heading {
    font-size: 2.75rem;
  }

  .main-description {
    font-size: var(--font-size-xl);
  }

  .cta-button {
    padding: 1.25rem 2.5rem;
    font-size: var(--font-size-lg);
  }
}

/* Mobile adjustments */
@media (max-width: 767px) {
  .main-heading {
    font-size: var(--font-size-2xl);
    line-height: 1.3;
  }

  .main-description {
    font-size: var(--font-size-base);
  }

  .card-subtitle {
    font-size: var(--font-size-xs);
    text-align: center;
  }

  .cta-button {
    padding: 0.875rem 1.75rem;
    font-size: var(--font-size-sm);
    min-width: 120px;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .main-heading {
    font-size: var(--font-size-xl);
  }

  .main-description {
    font-size: var(--font-size-sm);
  }

  .cta-button {
    padding: 0.75rem 1.5rem;
    font-size: var(--font-size-sm);
    min-width: 100px;
  }
}

/* ===================================
   ACCESSIBILITY ENHANCEMENTS
   =================================== */

/* High contrast mode */
@media (prefers-contrast: high) {
  .main-heading {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  }

  .main-description {
    color: #e0e0e0;
  }

  .cta-button {
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.6);
    color: #ffffff;
  }

  .cta-button:hover {
    background: rgba(255, 255, 255, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.8);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .cta-button {
    transition: none;
  }

  .cta-button::after {
    display: none;
  }

  .cta-button:hover {
    transform: none;
  }

  .cta-button:active {
    transform: none;
  }
}

/* ===================================
   REALISTIC KNOBS WITH GRADIENTS AND TRANSFORMS
   =================================== */

/* Knob container */
.knob-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

/* Main knob styling */
.knob {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  position: relative;
  cursor: grab;
  user-select: none;
  transition: all var(--transition-medium);

  /* Realistic knob gradient */
  background: linear-gradient(145deg,
    #f8f8f8 0%,
    #e8e8e8 25%,
    #d8d8d8 50%,
    #c8c8c8 75%,
    #b8b8b8 100%);

  /* Multi-layered shadows for depth */
  box-shadow:
    /* Outer shadow */
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1),
    /* Inner highlights */
    inset 0 2px 4px rgba(255, 255, 255, 0.8),
    inset 0 -2px 4px rgba(0, 0, 0, 0.1),
    /* Rim shadow */
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
}

/* Knob active state */
.knob:active {
  cursor: grabbing;
  transform: scale(0.98);
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.15),
    inset 0 2px 4px rgba(0, 0, 0, 0.15),
    inset 0 -1px 2px rgba(255, 255, 255, 0.6),
    inset 0 0 0 1px rgba(255, 255, 255, 0.3);
}

/* Knob hover effect */
.knob:hover {
  background: linear-gradient(145deg,
    #fafafa 0%,
    #eaeaea 25%,
    #dadada 50%,
    #cacaca 75%,
    #bababa 100%);

  box-shadow:
    0 6px 16px rgba(0, 0, 0, 0.18),
    0 3px 6px rgba(0, 0, 0, 0.12),
    inset 0 2px 4px rgba(255, 255, 255, 0.9),
    inset 0 -2px 4px rgba(0, 0, 0, 0.08),
    inset 0 0 0 1px rgba(255, 255, 255, 0.6);
}

/* Knob indicator (pointer) */
.knob-indicator {
  position: absolute;
  top: 8px;
  left: 50%;
  width: 3px;
  height: 20px;
  background: linear-gradient(180deg,
    #666666 0%,
    #444444 50%,
    #333333 100%);
  border-radius: 1.5px;
  transform: translateX(-50%);
  transform-origin: center 26px;
  transition: transform var(--transition-medium);

  /* Indicator shadow */
  box-shadow:
    0 1px 2px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Knob center dot */
.knob::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  background: radial-gradient(circle,
    #333333 0%,
    #555555 50%,
    #777777 100%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow:
    0 1px 2px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  z-index: 2;
}

/* Knob rim highlight */
.knob::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border-radius: 50%;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.4) 0%,
    transparent 30%,
    transparent 70%,
    rgba(0, 0, 0, 0.1) 100%);
  pointer-events: none;
  z-index: 1;
}

/* ===================================
   KNOB SIZE VARIATIONS
   =================================== */

/* Large knobs */
.knob.large {
  width: 80px;
  height: 80px;
}

.knob.large .knob-indicator {
  top: 10px;
  height: 25px;
  transform-origin: center 35px;
}

.knob.large::before {
  width: 10px;
  height: 10px;
}

/* Small knobs */
.knob.small {
  width: 45px;
  height: 45px;
}

.knob.small .knob-indicator {
  top: 6px;
  height: 15px;
  width: 2px;
  transform-origin: center 20px;
}

.knob.small::before {
  width: 6px;
  height: 6px;
}

/* ===================================
   KNOB INTERACTION STATES
   =================================== */

/* Focus state for accessibility */
.knob:focus-visible {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
}

/* Disabled state */
.knob.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.knob.disabled .knob-indicator {
  background: linear-gradient(180deg,
    #999999 0%,
    #777777 50%,
    #666666 100%);
}

/* ===================================
   RESPONSIVE KNOB ADJUSTMENTS
   =================================== */

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .knob {
    width: 65px;
    height: 65px;
  }

  .knob-indicator {
    top: 9px;
    height: 22px;
    transform-origin: center 28px;
  }
}

/* Desktop adjustments */
@media (min-width: 1024px) {
  .knob {
    width: 70px;
    height: 70px;
  }

  .knob-indicator {
    top: 10px;
    height: 24px;
    transform-origin: center 30px;
  }

  .knob::before {
    width: 9px;
    height: 9px;
  }
}

/* Mobile adjustments */
@media (max-width: 767px) {
  .knob {
    width: 50px;
    height: 50px;
  }

  .knob-indicator {
    top: 7px;
    height: 18px;
    transform-origin: center 22px;
  }

  .knob::before {
    width: 7px;
    height: 7px;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .knob {
    width: 45px;
    height: 45px;
  }

  .knob-indicator {
    top: 6px;
    height: 16px;
    width: 2px;
    transform-origin: center 20px;
  }

  .knob::before {
    width: 6px;
    height: 6px;
  }
}

/* ===================================
   SLIDER CONTROLS WITH GLASS STYLING
   =================================== */

/* Slider group container */
.slider-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  height: 100%;
}

/* Slider track */
.slider-track {
  width: 6px;
  height: 100px;
  background: linear-gradient(180deg,
    #e8e8e8 0%,
    #d0d0d0 50%,
    #c0c0c0 100%);
  border-radius: 3px;
  position: relative;
  cursor: pointer;

  /* Track shadows for depth */
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.2),
    inset 0 -1px 2px rgba(255, 255, 255, 0.5),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Slider handle */
.slider-handle {
  position: absolute;
  left: 50%;
  width: 16px;
  height: 16px;
  background: linear-gradient(145deg,
    #ffffff 0%,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #d0d0d0 75%,
    #c0c0c0 100%);
  border-radius: 50%;
  transform: translateX(-50%);
  cursor: grab;
  transition: all var(--transition-medium);

  /* Handle shadows */
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.15),
    0 1px 2px rgba(0, 0, 0, 0.1),
    inset 0 1px 2px rgba(255, 255, 255, 0.8),
    inset 0 -1px 1px rgba(0, 0, 0, 0.1);
}

/* Handle active state */
.slider-handle:active {
  cursor: grabbing;
  transform: translateX(-50%) scale(1.1);
  box-shadow:
    0 3px 8px rgba(0, 0, 0, 0.2),
    0 1px 3px rgba(0, 0, 0, 0.15),
    inset 0 1px 2px rgba(255, 255, 255, 0.9),
    inset 0 -1px 1px rgba(0, 0, 0, 0.08);
}

/* Handle hover effect */
.slider-handle:hover {
  background: linear-gradient(145deg,
    #ffffff 0%,
    #f2f2f2 25%,
    #e2e2e2 50%,
    #d2d2d2 75%,
    #c2c2c2 100%);

  box-shadow:
    0 3px 8px rgba(0, 0, 0, 0.18),
    0 2px 4px rgba(0, 0, 0, 0.12),
    inset 0 1px 2px rgba(255, 255, 255, 0.9),
    inset 0 -1px 1px rgba(0, 0, 0, 0.08);
}

/* Slider track fill (shows current value) */
.slider-track::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--slider-fill, 50%);
  background: linear-gradient(180deg,
    var(--accent-blue) 0%,
    #3a8eff 50%,
    #2a7eff 100%);
  border-radius: inherit;
  transition: height var(--transition-medium);

  /* Fill glow effect */
  box-shadow:
    0 0 4px rgba(74, 158, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* ===================================
   SPEED CONTROLS
   =================================== */

.speed-controls {
  display: flex;
  gap: 0.5rem;
  margin: 1rem 0;
}

.speed-btn {
  padding: 0.5rem 1rem;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--light-text-secondary);
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all var(--transition-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

@supports (backdrop-filter: blur(1px)) {
  .speed-btn {
    background: rgba(255, 255, 255, 0.2);
  }
}

.speed-btn:hover {
  background: rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.5);
  color: var(--light-text-primary);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@supports (backdrop-filter: blur(1px)) {
  .speed-btn:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}

.speed-btn.active {
  background: var(--accent-blue);
  border: 1px solid var(--accent-blue);
  color: white;
  box-shadow:
    0 2px 8px rgba(74, 158, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.speed-btn:active {
  transform: translateY(0);
  box-shadow:
    0 1px 4px rgba(0, 0, 0, 0.15),
    inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* ===================================
   TOGGLE SWITCHES
   =================================== */

.toggle-switch {
  position: relative;
  width: 50px;
  height: 24px;
}

.toggle-switch input[type="checkbox"] {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #cccccc;
  border-radius: 12px;
  transition: all var(--transition-medium);

  /* Toggle track shadows */
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.2),
    inset 0 -1px 2px rgba(255, 255, 255, 0.5),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

.toggle-slider::before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background: linear-gradient(145deg,
    #ffffff 0%,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #d0d0d0 75%,
    #c0c0c0 100%);
  border-radius: 50%;
  transition: all var(--transition-medium);

  /* Toggle handle shadows */
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.1),
    inset 0 1px 1px rgba(255, 255, 255, 0.8);
}

input:checked + .toggle-slider {
  background: var(--accent-blue);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 -1px 2px rgba(255, 255, 255, 0.3),
    0 1px 2px rgba(0, 0, 0, 0.1),
    0 0 8px rgba(74, 158, 255, 0.3);
}

input:checked + .toggle-slider::before {
  transform: translateX(26px);
  background: linear-gradient(145deg,
    #ffffff 0%,
    #f8f8f8 25%,
    #f0f0f0 50%,
    #e8e8e8 75%,
    #e0e0e0 100%);
}

.toggle-slider:hover::before {
  box-shadow:
    0 3px 6px rgba(0, 0, 0, 0.25),
    0 1px 3px rgba(0, 0, 0, 0.15),
    inset 0 1px 1px rgba(255, 255, 255, 0.9);
}

/* ===================================
   FREQUENCY GRAPH VISUALIZATION
   =================================== */

/* Graph container */
.frequency-graph {
  position: relative;
  width: 100%;
  height: 80px;
  margin: 1rem 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-small);
  overflow: hidden;
}

@supports (backdrop-filter: blur(1px)) {
  .frequency-graph {
    background: rgba(255, 255, 255, 0.08);
  }
}

/* Graph grid background */
.graph-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;

  /* Grid pattern using CSS gradients */
  background-image:
    /* Vertical lines */
    linear-gradient(90deg, transparent 0%, transparent 24%, rgba(255, 255, 255, 0.2) 25%, rgba(255, 255, 255, 0.2) 26%, transparent 27%),
    linear-gradient(90deg, transparent 0%, transparent 49%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 51%, transparent 52%),
    linear-gradient(90deg, transparent 0%, transparent 74%, rgba(255, 255, 255, 0.2) 75%, rgba(255, 255, 255, 0.2) 76%, transparent 77%),
    /* Horizontal lines */
    linear-gradient(0deg, transparent 0%, transparent 24%, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.1) 26%, transparent 27%),
    linear-gradient(0deg, transparent 0%, transparent 49%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.1) 51%, transparent 52%),
    linear-gradient(0deg, transparent 0%, transparent 74%, rgba(255, 255, 255, 0.1) 75%, rgba(255, 255, 255, 0.1) 76%, transparent 77%);

  background-size: 100% 100%;
}

/* Graph curve SVG styling */
.graph-curve {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.graph-curve path {
  stroke-width: 2;
  fill: none;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

/* Primary curve (orange) */
.graph-curve path:first-child {
  stroke: #ff6b35;
  stroke-width: 2.5;
  filter: drop-shadow(0 0 4px rgba(255, 107, 53, 0.4));
}

/* Secondary curve (gray) */
.graph-curve path:last-child {
  stroke: #999999;
  stroke-width: 1.5;
  opacity: 0.7;
}

/* Graph labels */
.graph-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  padding: 0 0.5rem;
  font-size: var(--font-size-xs);
  color: var(--light-text-muted);
  font-weight: var(--font-weight-medium);
}

/* Graph value indicators */
.graph-indicators {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

.graph-indicator {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--accent-blue);
  border-radius: 50%;
  box-shadow:
    0 0 4px var(--accent-blue-glow),
    0 0 8px var(--accent-blue-glow);
  animation: indicatorPulse 2s ease-in-out infinite;
}

.graph-indicator:nth-child(1) {
  top: 60%;
  left: 20%;
  animation-delay: 0s;
}

.graph-indicator:nth-child(2) {
  top: 40%;
  left: 50%;
  animation-delay: 0.5s;
}

.graph-indicator:nth-child(3) {
  top: 30%;
  left: 80%;
  animation-delay: 1s;
}

@keyframes indicatorPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* ===================================
   POWER BUTTON
   =================================== */

.power-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-medium);
  color: var(--light-text-secondary);
}

@supports (backdrop-filter: blur(1px)) {
  .power-btn {
    background: rgba(255, 255, 255, 0.15);
  }
}

.power-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: var(--light-text-primary);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@supports (backdrop-filter: blur(1px)) {
  .power-btn:hover {
    background: rgba(255, 255, 255, 0.25);
  }
}

.power-btn:active {
  transform: scale(0.95);
  box-shadow:
    0 1px 4px rgba(0, 0, 0, 0.15),
    inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.power-btn svg {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

/* ===================================
   RESPONSIVE GRAPH ADJUSTMENTS
   =================================== */

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .frequency-graph {
    height: 90px;
  }

  .power-btn {
    width: 36px;
    height: 36px;
  }

  .power-btn svg {
    width: 18px;
    height: 18px;
  }
}

/* Desktop adjustments */
@media (min-width: 1024px) {
  .frequency-graph {
    height: 100px;
  }

  .power-btn {
    width: 40px;
    height: 40px;
  }

  .power-btn svg {
    width: 20px;
    height: 20px;
  }
}

/* Mobile adjustments */
@media (max-width: 767px) {
  .frequency-graph {
    height: 70px;
  }

  .graph-labels {
    font-size: 10px;
  }

  .power-btn {
    width: 28px;
    height: 28px;
  }

  .power-btn svg {
    width: 14px;
    height: 14px;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .frequency-graph {
    height: 60px;
  }

  .graph-labels {
    font-size: 9px;
    padding: 0 0.25rem;
  }
}

/* ===================================
   ACCESSIBILITY FOCUS INDICATORS
   =================================== */

/* Enhanced focus styles for all interactive elements */
.knob:focus-visible,
.slider-track:focus-visible,
.cta-button:focus-visible,
.social-icon:focus-visible,
.speed-btn:focus-visible,
.power-btn:focus-visible,
.theme-btn:focus-visible {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
  border-radius: var(--border-radius-small);
}

/* Special focus for round elements */
.knob:focus-visible,
.power-btn:focus-visible,
.theme-btn:focus-visible {
  border-radius: 50%;
}

/* Toggle switch focus */
.toggle-slider:focus-visible,
input[type="checkbox"]:focus-visible + .toggle-slider {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
  border-radius: 12px;
}

/* High contrast focus indicators */
@media (prefers-contrast: high) {
  .knob:focus-visible,
  .slider-track:focus-visible,
  .cta-button:focus-visible,
  .social-icon:focus-visible,
  .speed-btn:focus-visible,
  .power-btn:focus-visible,
  .theme-btn:focus-visible {
    outline: 3px solid #ffffff;
    outline-offset: 2px;
    box-shadow: 0 0 0 5px #000000;
  }
}

/* Focus visible only when using keyboard navigation */
.knob:focus:not(:focus-visible),
.slider-track:focus:not(:focus-visible),
.cta-button:focus:not(:focus-visible),
.social-icon:focus:not(:focus-visible),
.speed-btn:focus:not(:focus-visible),
.power-btn:focus:not(:focus-visible),
.theme-btn:focus:not(:focus-visible) {
  outline: none;
}

/* ===================================
   ARIA LIVE REGIONS AND SCREEN READER SUPPORT
   =================================== */

/* Screen reader only text */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Ensure interactive elements have proper ARIA attributes */
.knob[role="slider"] {
  cursor: pointer;
}

.knob[role="slider"]:focus-visible {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
}

/* Skip link for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--accent-blue);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: var(--z-modal);
  transition: top var(--transition-medium);
}

.skip-link:focus {
  top: 6px;
}

/* ===================================
   REDUCED MOTION PREFERENCES
   =================================== */

@media (prefers-reduced-motion: reduce) {
  /* Disable all animations and transitions */
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Remove floating animations */
  .circle,
  .gradient-orb,
  .side-glass {
    animation: none;
  }

  /* Remove glow animations */
  .top-light-bar,
  .dot,
  .yellow-accent,
  .line {
    animation: none;
  }

  /* Remove shimmer effects */
  .shimmer-effect::before,
  .social-icon::before,
  .cta-button::after {
    display: none;
  }
}

/* ===================================
   HIGH CONTRAST MODE SUPPORT
   =================================== */

@media (prefers-contrast: high) {
  /* Increase contrast for glass elements */
  .glass-card,
  .control-panel {
    background: rgba(255, 255, 255, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.8);
  }

  .light-theme .glass-card,
  .light-theme .control-panel {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(0, 0, 0, 0.3);
  }

  /* Increase text contrast */
  .main-heading {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  }

  .main-description {
    color: #e0e0e0;
  }

  .light-theme .main-heading {
    color: #000000;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  }

  .light-theme .main-description {
    color: #333333;
  }

  /* Increase button contrast */
  .cta-button {
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.6);
    color: #ffffff;
  }

  .cta-button:hover {
    background: rgba(255, 255, 255, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.8);
  }

  /* Increase control contrast */
  .knob {
    border: 2px solid rgba(0, 0, 0, 0.3);
  }

  .slider-track {
    border: 1px solid rgba(0, 0, 0, 0.3);
  }

  .toggle-slider {
    border: 1px solid rgba(0, 0, 0, 0.3);
  }
}

/* ===================================
   TOUCH DEVICE OPTIMIZATIONS
   =================================== */

@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets */
  .knob {
    min-width: 44px;
    min-height: 44px;
  }

  .social-icon {
    min-width: 44px;
    min-height: 44px;
  }

  .power-btn {
    min-width: 44px;
    min-height: 44px;
  }

  .speed-btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
  }

  .toggle-switch {
    min-width: 60px;
    min-height: 32px;
  }

  /* Remove hover effects on touch devices */
  .knob:hover,
  .social-icon:hover,
  .power-btn:hover,
  .speed-btn:hover {
    transform: none;
  }

  /* Add active states for touch feedback */
  .knob:active,
  .social-icon:active,
  .power-btn:active,
  .speed-btn:active {
    transform: scale(0.95);
    opacity: 0.8;
  }
}
