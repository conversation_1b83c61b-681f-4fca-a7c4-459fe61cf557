/**
 * GLASSMORPHISM UI SHOWCASE
 * Interactive JavaScript for theme switching and component interactions
 */

// Theme management
class ThemeManager {
  constructor() {
    this.darkTheme = document.getElementById('darkTheme');
    this.lightTheme = document.getElementById('lightTheme');
    this.themeToggle = document.getElementById('themeToggle');
    this.themeIcon = this.themeToggle.querySelector('.theme-icon');
    
    this.currentTheme = 'dark';
    this.init();
  }
  
  init() {
    this.themeToggle.addEventListener('click', () => this.toggleTheme());
    
    // Set initial theme
    this.setTheme(this.currentTheme);
    
    // Add entrance animations
    this.addEntranceAnimations();
  }
  
  toggleTheme() {
    this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
    this.setTheme(this.currentTheme);
  }
  
  setTheme(theme) {
    if (theme === 'dark') {
      this.darkTheme.style.display = 'flex';
      this.lightTheme.style.display = 'none';
      this.themeIcon.textContent = '🌙';
      this.themeToggle.setAttribute('aria-label', 'Switch to light theme');
    } else {
      this.darkTheme.style.display = 'none';
      this.lightTheme.style.display = 'flex';
      this.themeIcon.textContent = '☀️';
      this.themeToggle.setAttribute('aria-label', 'Switch to dark theme');
    }
    
    // Trigger entrance animations for the new theme
    setTimeout(() => this.addEntranceAnimations(), 100);
  }
  
  addEntranceAnimations() {
    const activeContainer = this.currentTheme === 'dark' ? this.darkTheme : this.lightTheme;
    const animatedElements = activeContainer.querySelectorAll('.glass-card, .control-panel');
    
    animatedElements.forEach((element, index) => {
      element.style.opacity = '0';
      element.style.transform = 'translateY(20px)';
      
      setTimeout(() => {
        element.style.transition = 'all 0.5s ease-out';
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
      }, index * 100);
    });
  }
}

// Knob control interactions
class KnobController {
  constructor() {
    this.knobs = document.querySelectorAll('.knob');
    this.init();
  }
  
  init() {
    this.knobs.forEach(knob => {
      this.setupKnob(knob);
    });
  }
  
  setupKnob(knob) {
    let isDragging = false;
    let startAngle = 0;
    let currentValue = parseInt(knob.dataset.value) || 50;
    
    const indicator = knob.querySelector('.knob-indicator');
    this.updateKnobRotation(indicator, currentValue);
    
    // Mouse events
    knob.addEventListener('mousedown', (e) => {
      isDragging = true;
      startAngle = this.getAngle(e, knob);
      knob.style.cursor = 'grabbing';
      e.preventDefault();
    });
    
    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;
      
      const angle = this.getAngle(e, knob);
      const deltaAngle = angle - startAngle;
      currentValue = Math.max(0, Math.min(100, currentValue + deltaAngle / 3));
      
      this.updateKnobRotation(indicator, currentValue);
      knob.dataset.value = Math.round(currentValue);
      startAngle = angle;
    });
    
    document.addEventListener('mouseup', () => {
      if (isDragging) {
        isDragging = false;
        knob.style.cursor = 'grab';
      }
    });
    
    // Touch events for mobile
    knob.addEventListener('touchstart', (e) => {
      isDragging = true;
      startAngle = this.getAngle(e.touches[0], knob);
      e.preventDefault();
    });
    
    document.addEventListener('touchmove', (e) => {
      if (!isDragging) return;
      
      const angle = this.getAngle(e.touches[0], knob);
      const deltaAngle = angle - startAngle;
      currentValue = Math.max(0, Math.min(100, currentValue + deltaAngle / 3));
      
      this.updateKnobRotation(indicator, currentValue);
      knob.dataset.value = Math.round(currentValue);
      startAngle = angle;
      e.preventDefault();
    });
    
    document.addEventListener('touchend', () => {
      isDragging = false;
    });
    
    // Keyboard accessibility
    knob.setAttribute('tabindex', '0');
    knob.setAttribute('role', 'slider');
    knob.setAttribute('aria-valuemin', '0');
    knob.setAttribute('aria-valuemax', '100');
    knob.setAttribute('aria-valuenow', currentValue);
    
    knob.addEventListener('keydown', (e) => {
      let newValue = currentValue;
      
      switch(e.key) {
        case 'ArrowUp':
        case 'ArrowRight':
          newValue = Math.min(100, currentValue + 5);
          break;
        case 'ArrowDown':
        case 'ArrowLeft':
          newValue = Math.max(0, currentValue - 5);
          break;
        case 'Home':
          newValue = 0;
          break;
        case 'End':
          newValue = 100;
          break;
        default:
          return;
      }
      
      currentValue = newValue;
      this.updateKnobRotation(indicator, currentValue);
      knob.dataset.value = Math.round(currentValue);
      knob.setAttribute('aria-valuenow', Math.round(currentValue));
      e.preventDefault();
    });
  }
  
  getAngle(event, element) {
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    return Math.atan2(event.clientY - centerY, event.clientX - centerX) * 180 / Math.PI;
  }
  
  updateKnobRotation(indicator, value) {
    // Map 0-100 value to -135 to +135 degrees
    const rotation = (value - 50) * 2.7;
    indicator.style.transform = `rotate(${rotation}deg)`;
  }
}

// Slider control interactions
class SliderController {
  constructor() {
    this.sliders = document.querySelectorAll('.slider-track');
    this.init();
  }
  
  init() {
    this.sliders.forEach(slider => {
      this.setupSlider(slider);
    });
  }
  
  setupSlider(track) {
    const handle = track.querySelector('.slider-handle');
    let isDragging = false;
    let currentValue = 50; // Default value
    
    // Set initial position
    handle.style.bottom = `${currentValue}%`;
    
    track.addEventListener('mousedown', (e) => {
      isDragging = true;
      this.updateSliderValue(e, track, handle);
      e.preventDefault();
    });
    
    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;
      this.updateSliderValue(e, track, handle);
    });
    
    document.addEventListener('mouseup', () => {
      isDragging = false;
    });
    
    // Touch events
    track.addEventListener('touchstart', (e) => {
      isDragging = true;
      this.updateSliderValue(e.touches[0], track, handle);
      e.preventDefault();
    });
    
    document.addEventListener('touchmove', (e) => {
      if (!isDragging) return;
      this.updateSliderValue(e.touches[0], track, handle);
      e.preventDefault();
    });
    
    document.addEventListener('touchend', () => {
      isDragging = false;
    });
  }
  
  updateSliderValue(event, track, handle) {
    const rect = track.getBoundingClientRect();
    const y = event.clientY - rect.top;
    const percentage = Math.max(0, Math.min(100, 100 - (y / rect.height) * 100));
    
    handle.style.bottom = `${percentage}%`;
  }
}

// Button interactions
class ButtonController {
  constructor() {
    this.buttons = document.querySelectorAll('.cta-button, .speed-btn, .power-btn');
    this.init();
  }
  
  init() {
    this.buttons.forEach(button => {
      this.setupButton(button);
    });
    
    // Speed button group behavior
    this.setupSpeedButtons();
  }
  
  setupButton(button) {
    button.addEventListener('click', (e) => {
      // Add click animation
      button.style.transform = 'scale(0.95)';
      setTimeout(() => {
        button.style.transform = '';
      }, 150);
    });
    
    // Add ripple effect
    button.addEventListener('click', (e) => {
      const ripple = document.createElement('span');
      const rect = button.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;
      
      ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
      `;
      
      button.style.position = 'relative';
      button.style.overflow = 'hidden';
      button.appendChild(ripple);
      
      setTimeout(() => ripple.remove(), 600);
    });
  }
  
  setupSpeedButtons() {
    const speedButtons = document.querySelectorAll('.speed-btn');
    
    speedButtons.forEach(button => {
      button.addEventListener('click', () => {
        speedButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
      });
    });
  }
}

// Initialize all controllers when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ThemeManager();
  new KnobController();
  new SliderController();
  new ButtonController();
  
  // Add ripple animation keyframes
  const style = document.createElement('style');
  style.textContent = `
    @keyframes ripple {
      to {
        transform: scale(2);
        opacity: 0;
      }
    }
  `;
  document.head.appendChild(style);
});

// Performance optimization: Debounce resize events
let resizeTimeout;
window.addEventListener('resize', () => {
  clearTimeout(resizeTimeout);
  resizeTimeout = setTimeout(() => {
    // Recalculate positions if needed
    console.log('Window resized - recalculating positions');
  }, 250);
});
